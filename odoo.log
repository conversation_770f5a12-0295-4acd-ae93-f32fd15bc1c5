2025-10-07 19:56:47,553 25176 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 19:56:47,554 25176 INFO ? odoo: Odoo version 19.0 
2025-10-07 19:56:47,554 25176 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 19:56:47,554 25176 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 19:56:47,555 25176 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 19:56:47,579 25176 CRITICAL ? odoo.modules.module: Couldn't load module base 
2025-10-07 19:56:47,580 25176 ERROR ? odoo.service.server: Failed to load server-wide module `base`. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 3, in <module>
    from . import assetsbundle
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\assetsbundle.py", line 14, in <module>
    from rjsmin import jsmin as rjsmin
ModuleNotFoundError: No module named 'rjsmin'
2025-10-07 19:56:47,886 25176 CRITICAL ? odoo.modules.module: Couldn't load module web 
2025-10-07 19:56:47,887 25176 ERROR ? odoo.service.server: Failed to load server-wide module `web`.
    The `web` module is provided by the addons found in the `openerp-web` project.
    Maybe you forgot to add those addons in your addons_path configuration. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "d:\odoo_19.0_new\odoo_src\addons\web\__init__.py", line 4, in <module>
    from . import controllers
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\__init__.py", line 4, in <module>
    from . import binary
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\binary.py", line 20, in <module>
    from odoo.addons.base.models.assetsbundle import ANY_UNIQUE
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 3, in <module>
    from . import assetsbundle
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\assetsbundle.py", line 14, in <module>
    from rjsmin import jsmin as rjsmin
ModuleNotFoundError: No module named 'rjsmin'
2025-10-07 19:56:48,102 25176 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 19:59:13,212 4900 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 19:59:13,213 4900 INFO ? odoo: Odoo version 19.0 
2025-10-07 19:59:13,213 4900 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 19:59:13,213 4900 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 19:59:13,213 4900 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 19:59:13,872 4900 CRITICAL ? odoo.modules.module: Couldn't load module base 
2025-10-07 19:59:13,872 4900 ERROR ? odoo.service.server: Failed to load server-wide module `base`. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 44, in <module>
    pypdf = importlib.import_module(SUBMOD, __spec__.name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\_pypdf2_2.py", line 1, in <module>
    from PyPDF2 import errors, filters, generic, PdfReader, PdfWriter as _Writer
ModuleNotFoundError: No module named 'PyPDF2'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 50, in <module>
    raise ImportError("pypdf implementation not found") from error
ImportError: pypdf implementation not found
2025-10-07 19:59:13,959 4900 CRITICAL ? odoo.modules.module: Couldn't load module web 
2025-10-07 19:59:13,959 4900 ERROR ? odoo.service.server: Failed to load server-wide module `web`.
    The `web` module is provided by the addons found in the `openerp-web` project.
    Maybe you forgot to add those addons in your addons_path configuration. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 44, in <module>
    pypdf = importlib.import_module(SUBMOD, __spec__.name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\_pypdf2_2.py", line 1, in <module>
    from PyPDF2 import errors, filters, generic, PdfReader, PdfWriter as _Writer
ModuleNotFoundError: No module named 'PyPDF2'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "d:\odoo_19.0_new\odoo_src\addons\web\__init__.py", line 4, in <module>
    from . import controllers
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\__init__.py", line 5, in <module>
    from . import database
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\database.py", line 19, in <module>
    from odoo.addons.base.models.ir_qweb import render as qweb_render
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 50, in <module>
    raise ImportError("pypdf implementation not found") from error
ImportError: pypdf implementation not found
2025-10-07 19:59:14,142 4900 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:01:27,769 11372 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:01:27,770 11372 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:01:27,770 11372 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:01:27,770 11372 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:01:27,770 11372 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:01:28,253 11372 CRITICAL ? odoo.modules.module: Couldn't load module base 
2025-10-07 20:01:28,254 11372 ERROR ? odoo.service.server: Failed to load server-wide module `base`. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 22, in <module>
    from . import ir_mail_server
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_mail_server.py", line 17, in <module>
    from OpenSSL import crypto as SSLCrypto
ModuleNotFoundError: No module named 'OpenSSL'
2025-10-07 20:01:28,286 11372 CRITICAL ? odoo.modules.module: Couldn't load module web 
2025-10-07 20:01:28,286 11372 ERROR ? odoo.service.server: Failed to load server-wide module `web`.
    The `web` module is provided by the addons found in the `openerp-web` project.
    Maybe you forgot to add those addons in your addons_path configuration. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "d:\odoo_19.0_new\odoo_src\addons\web\__init__.py", line 4, in <module>
    from . import controllers
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\__init__.py", line 5, in <module>
    from . import database
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\database.py", line 19, in <module>
    from odoo.addons.base.models.ir_qweb import render as qweb_render
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 22, in <module>
    from . import ir_mail_server
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_mail_server.py", line 17, in <module>
    from OpenSSL import crypto as SSLCrypto
ModuleNotFoundError: No module named 'OpenSSL'
2025-10-07 20:01:28,480 11372 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:01:28,595 11372 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:01:28,675 11372 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:18:15,976 22172 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:18:15,978 22172 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:18:15,978 22172 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:18:15,978 22172 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:18:15,978 22172 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:18:16,398 22172 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 20:18:19,474 22172 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:18:19,551 22172 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:18:19,725 22172 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:06,672 26332 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:21:06,673 26332 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:21:06,673 26332 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:21:06,673 26332 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:21:06,673 26332 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:21:06,932 26332 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 20:21:07,438 26332 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-10-07 20:21:07,445 26332 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:21:07,507 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:07,576 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:36,938 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:36,975 26332 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 20:21:36] "GET / HTTP/1.1" 303 - 0 0.000 0.163
2025-10-07 20:21:37,152 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:37,248 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:37,249 26332 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 20:21:37] "GET /odoo HTTP/1.1" 303 - 0 0.000 0.210
2025-10-07 20:21:37,350 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:38,284 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:21:38,431 26332 INFO ? werkzeug: 127.0.0.1 - - [07/Oct/2025 20:21:38] "GET /web/database/selector HTTP/1.1" 200 - 0 0.000 1.151
2025-10-07 20:22:57,193 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:23:00,326 26332 INFO None odoo.service.db: Create database `school`. 
2025-10-07 20:23:00,491 26332 INFO None odoo.sql_db: Connection to the database failed 
2025-10-07 20:23:00,491 26332 ERROR None odoo.addons.web.controllers.database: Database creation error. 
Traceback (most recent call last):
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\database.py", line 81, in create
    dispatch_rpc('db', 'create_database', [master_pwd, name, bool(post.get('demo')), lang, password, post['login'], country_code, post['phone']])
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\http.py", line 439, in dispatch_rpc
    return dispatch(method, params)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 519, in dispatch
    return g[exp_method_name](*params)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 52, in if_db_mgt_enabled
    return func(*args, **kwargs)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 180, in exp_create_database
    _create_empty_database(db_name)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 130, in _create_empty_database
    with closing(db.cursor()) as cr:
                 ~~~~~~~~~^^
  File "D:\odoo_19.0_new\odoo_src\odoo\sql_db.py", line 756, in cursor
    return Cursor(self.__pool, self.__dbname, self.__dsn)
  File "D:\odoo_19.0_new\odoo_src\odoo\sql_db.py", line 358, in __init__
    self._cnx: PsycoConnection = pool.borrow(dsn)
                                 ~~~~~~~~~~~^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\func.py", line 88, in locked
    return func(inst, *args, **kwargs)
  File "D:\odoo_19.0_new\odoo_src\odoo\sql_db.py", line 680, in borrow
    result = psycopg2.connect(
        connection_factory=PsycoConnection,
        **connection_info)
  File "D:\odoo_19.0_new\venv\Lib\site-packages\psycopg2\__init__.py", line 135, in connect
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
psycopg2.OperationalError: connection to server at "localhost" (::1), port 5432 failed: FATAL:  password authentication failed for user "odoo"

2025-10-07 20:23:01,468 26332 INFO None odoo.sql_db: Connection to the database failed 
2025-10-07 20:23:01,531 26332 INFO None werkzeug: 127.0.0.1 - - [07/Oct/2025 20:23:01] "POST /web/database/create HTTP/1.1" 200 - 0 0.000 4.405
2025-10-07 20:23:35,038 26332 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:23:36,664 26332 ERROR None odoo.addons.web.controllers.database: Database creation error. 
Traceback (most recent call last):
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\database.py", line 81, in create
    dispatch_rpc('db', 'create_database', [master_pwd, name, bool(post.get('demo')), lang, password, post['login'], country_code, post['phone']])
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\http.py", line 439, in dispatch_rpc
    return dispatch(method, params)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 518, in dispatch
    check_super(passwd)
    ~~~~~~~~~~~^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\service\db.py", line 63, in check_super
    raise odoo.exceptions.AccessDenied()
odoo.exceptions.AccessDenied: Access Denied
2025-10-07 20:23:37,500 26332 INFO None odoo.sql_db: Connection to the database failed 
2025-10-07 20:23:37,566 26332 INFO None werkzeug: 127.0.0.1 - - [07/Oct/2025 20:23:37] "POST /web/database/create HTTP/1.1" 200 - 0 0.000 2.624
2025-10-07 20:25:07,452 26332 WARNING ? odoo.service.server: Thread <Thread(odoo.service.http.request.13252, started 13252)> virtual real time limit (130/120s) reached. 
2025-10-07 20:25:07,453 26332 INFO ? odoo.service.server: Dumping stacktrace of limit exceeding threads before reloading 
2025-10-07 20:25:07,538 26332 INFO ? odoo.tools.misc: 
# Thread: <Thread(odoo.service.http.request.13252, started 13252)> (db:n/a) (uid:n/a) (url:n/a) (qc:n/a qt:n/a pt:n/a)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1014, in _bootstrap
  self._bootstrap_inner()
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1043, in _bootstrap_inner
  self.run()
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 994, in run
  self._target(*self._args, **self._kwargs)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socketserver.py", line 697, in process_request_thread
  self.finish_request(request, client_address)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socketserver.py", line 362, in finish_request
  self.RequestHandlerClass(request, client_address, self)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socketserver.py", line 766, in __init__
  self.handle()
File: "D:\odoo_19.0_new\venv\Lib\site-packages\werkzeug\serving.py", line 398, in handle
  super().handle()
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\server.py", line 436, in handle
  self.handle_one_request()
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\server.py", line 404, in handle_one_request
  self.raw_requestline = self.rfile.readline(65537)
File: "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
  return self._sock.recv_into(b) 
2025-10-07 20:33:42,834 6860 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:34:57,177 14512 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:34:57,178 14512 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:34:57,178 14512 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:34:57,178 14512 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:34:57,178 14512 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:34:57,449 14512 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 20:34:57,993 14512 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:34:58,053 14512 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:34:58,122 14512 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:35:54,997 9800 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:35:54,998 9800 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:35:54,998 9800 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:35:54,998 9800 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:35:54,998 9800 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:35:55,275 9800 WARNING ? py.warnings: D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0_new\odoo_src\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0_new\odoo_src\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "D:\odoo_19.0_new\venv\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-07 20:35:55,769 9800 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:35:55,829 9800 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:35:56,028 9800 INFO ? odoo.sql_db: Connection to the database failed 
