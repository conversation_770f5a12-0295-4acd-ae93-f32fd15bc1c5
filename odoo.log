2025-10-07 19:56:47,553 25176 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 19:56:47,554 25176 INFO ? odoo: Odoo version 19.0 
2025-10-07 19:56:47,554 25176 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 19:56:47,554 25176 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 19:56:47,555 25176 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 19:56:47,579 25176 CRITICAL ? odoo.modules.module: Couldn't load module base 
2025-10-07 19:56:47,580 25176 ERROR ? odoo.service.server: Failed to load server-wide module `base`. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 3, in <module>
    from . import assetsbundle
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\assetsbundle.py", line 14, in <module>
    from rjsmin import jsmin as rjsmin
ModuleNotFoundError: No module named 'rjsmin'
2025-10-07 19:56:47,886 25176 CRITICAL ? odoo.modules.module: Couldn't load module web 
2025-10-07 19:56:47,887 25176 ERROR ? odoo.service.server: Failed to load server-wide module `web`.
    The `web` module is provided by the addons found in the `openerp-web` project.
    Maybe you forgot to add those addons in your addons_path configuration. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "d:\odoo_19.0_new\odoo_src\addons\web\__init__.py", line 4, in <module>
    from . import controllers
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\__init__.py", line 4, in <module>
    from . import binary
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\binary.py", line 20, in <module>
    from odoo.addons.base.models.assetsbundle import ANY_UNIQUE
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 3, in <module>
    from . import assetsbundle
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\assetsbundle.py", line 14, in <module>
    from rjsmin import jsmin as rjsmin
ModuleNotFoundError: No module named 'rjsmin'
2025-10-07 19:56:48,102 25176 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 19:59:13,212 4900 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 19:59:13,213 4900 INFO ? odoo: Odoo version 19.0 
2025-10-07 19:59:13,213 4900 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 19:59:13,213 4900 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 19:59:13,213 4900 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 19:59:13,872 4900 CRITICAL ? odoo.modules.module: Couldn't load module base 
2025-10-07 19:59:13,872 4900 ERROR ? odoo.service.server: Failed to load server-wide module `base`. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 44, in <module>
    pypdf = importlib.import_module(SUBMOD, __spec__.name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\_pypdf2_2.py", line 1, in <module>
    from PyPDF2 import errors, filters, generic, PdfReader, PdfWriter as _Writer
ModuleNotFoundError: No module named 'PyPDF2'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 50, in <module>
    raise ImportError("pypdf implementation not found") from error
ImportError: pypdf implementation not found
2025-10-07 19:59:13,959 4900 CRITICAL ? odoo.modules.module: Couldn't load module web 
2025-10-07 19:59:13,959 4900 ERROR ? odoo.service.server: Failed to load server-wide module `web`.
    The `web` module is provided by the addons found in the `openerp-web` project.
    Maybe you forgot to add those addons in your addons_path configuration. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 44, in <module>
    pypdf = importlib.import_module(SUBMOD, __spec__.name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\_pypdf2_2.py", line 1, in <module>
    from PyPDF2 import errors, filters, generic, PdfReader, PdfWriter as _Writer
ModuleNotFoundError: No module named 'PyPDF2'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "d:\odoo_19.0_new\odoo_src\addons\web\__init__.py", line 4, in <module>
    from . import controllers
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\__init__.py", line 5, in <module>
    from . import database
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\database.py", line 19, in <module>
    from odoo.addons.base.models.ir_qweb import render as qweb_render
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0_new\odoo_src\odoo\tools\pdf\__init__.py", line 50, in <module>
    raise ImportError("pypdf implementation not found") from error
ImportError: pypdf implementation not found
2025-10-07 19:59:14,142 4900 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:01:27,769 11372 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-07 20:01:27,770 11372 INFO ? odoo: Odoo version 19.0 
2025-10-07 20:01:27,770 11372 INFO ? odoo: Using configuration file at d:\odoo_19.0_new\odoo.conf 
2025-10-07 20:01:27,770 11372 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0_new\\odoo_src\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0_new\\odoo_src\\addons', 'd:\\odoo_19.0_new\\custom_addons']) 
2025-10-07 20:01:27,770 11372 INFO ? odoo: database: odoo@localhost:5432 
2025-10-07 20:01:28,253 11372 CRITICAL ? odoo.modules.module: Couldn't load module base 
2025-10-07 20:01:28,254 11372 ERROR ? odoo.service.server: Failed to load server-wide module `base`. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 22, in <module>
    from . import ir_mail_server
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_mail_server.py", line 17, in <module>
    from OpenSSL import crypto as SSLCrypto
ModuleNotFoundError: No module named 'OpenSSL'
2025-10-07 20:01:28,286 11372 CRITICAL ? odoo.modules.module: Couldn't load module web 
2025-10-07 20:01:28,286 11372 ERROR ? odoo.service.server: Failed to load server-wide module `web`.
    The `web` module is provided by the addons found in the `openerp-web` project.
    Maybe you forgot to add those addons in your addons_path configuration. 
Traceback (most recent call last):
  File "D:\odoo_19.0_new\odoo_src\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
    ~~~~~~~~~~~~~~~~~~~^^^
  File "D:\odoo_19.0_new\odoo_src\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
    ~~~~~~~~~~^^^^^^^^^^
  File "d:\odoo_19.0_new\odoo_src\addons\web\__init__.py", line 4, in <module>
    from . import controllers
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\__init__.py", line 5, in <module>
    from . import database
  File "d:\odoo_19.0_new\odoo_src\addons\web\controllers\database.py", line 19, in <module>
    from odoo.addons.base.models.ir_qweb import render as qweb_render
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\__init__.py", line 22, in <module>
    from . import ir_mail_server
  File "D:\odoo_19.0_new\odoo_src\odoo\addons\base\models\ir_mail_server.py", line 17, in <module>
    from OpenSSL import crypto as SSLCrypto
ModuleNotFoundError: No module named 'OpenSSL'
2025-10-07 20:01:28,480 11372 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-07 20:01:28,595 11372 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-07 20:01:28,675 11372 INFO ? odoo.sql_db: Connection to the database failed 
